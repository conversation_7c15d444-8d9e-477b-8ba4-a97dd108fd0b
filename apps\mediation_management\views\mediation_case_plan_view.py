#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : mediation_case_plan_view.py
<AUTHOR> JT_DA
@Date     : 2025/08/01
@File_Desc: 调解方案配置获取视图
"""

import logging
from django.db.models import Q, Case, When, IntegerField
from rest_framework.generics import GenericAPIView
from rest_framework import serializers


from apps.mediation_management.models import MediationCase, MediationPlan
from apps.mediation_management.serializers.mediation_case_query_serializers import (
    MediationPlanConfigSerializer,
)
from utils.ajax_result import AjaxResult
from utils.expression_calculator import calculate_expression_with_asset_data

# 获取日志记录器
logger = logging.getLogger(__name__)


class MediationPlanConfigView(GenericAPIView):
    """
    调解方案配置获取视图

    提供调解案件相关的调解方案配置信息获取和表达式计算功能。
    该视图通过调解案件ID查询对应的调解案件，获取所有相关的已生效调解方案的plan_config配置，
    并对配置中的表达式进行动态计算处理，返回按调解方案分组的嵌套结构数据。
    """

    # 配置序列化器类
    serializer_class = MediationPlanConfigSerializer

    def get(self, request, mediation_case_id, *args, **kwargs):
        """
        获取调解方案配置信息

        根据调解案件ID获取所有相关的已生效调解方案配置信息并进行表达式计算。
        该接口会查询与指定调解案件相关的所有已生效调解方案，对方案配置中的表达式进行动态计算，
        返回按调解方案分组的嵌套结构数据。

        **请求参数：**

        **路径参数：**
        - mediation_case_id (整数, 必需): 调解案件ID，通过URL路径参数传递

        **请求数据示例：**
        ```
        GET /mediation_management/mediation_case/123/plan_config/
        ```

        **响应数据结构：**

        成功响应：
        ```json
        {
            "code": 200,
            "msg": "获取成功",
            "state": "success",
            "data": [
                {
                    "plan_id": 1,
                    "plan_name": "标准还款方案",
                    "plan_config": [
                        {
                            "id": "PLAN001",
                            "title": "还款金额",
                            "logic_type": "result_calculation",
                            "expression": "{债权总额}*0.8",
                            "value": "120000.00"
                        },
                        {
                            "id": "PLAN002",
                            "title": "还款期限",
                            "logic_type": "text_formatting",
                            "expression": "12个月",
                            "value": "12个月"
                        }
                    ]
                },
                {
                    "plan_id": 2,
                    "plan_name": "优惠还款方案",
                    "plan_config": [
                        {
                            "id": "PLAN003",
                            "title": "优惠金额",
                            "logic_type": "result_calculation",
                            "expression": "{债权总额}*0.6",
                            "value": "90000.00"
                        }
                    ]
                }
            ]
        }
        ```

        错误响应：
        ```json
        {
            "code": 404,
            "msg": "未找到对应的调解案件",
            "state": "fail"
        }
        ```
        """
        try:
            # 验证调解案件ID参数
            serializer_data = {"mediation_case_id": mediation_case_id}
            serializer = self.get_serializer(data=serializer_data)
            serializer.is_valid(raise_exception=True)

            # 记录请求日志
            logger.info(f"开始处理调解方案配置获取，调解案件ID: {mediation_case_id}")

            # 获取调解案件对象
            mediation_case = MediationCase.objects.get(id=mediation_case_id)

            # 优先级策略：优先使用确认的方案配置快照，备用方案为查询已生效调解方案
            if mediation_case.confirmed_plan_config:
                # 优先使用：调解案件确认时的方案配置快照
                logger.info(f"使用调解案件 {mediation_case_id} 的确认方案配置快照")
                return self._process_confirmed_plan_config(mediation_case, mediation_case_id)
            else:
                # 备用方案：查询相关的已生效调解方案
                logger.info(f"使用调解案件 {mediation_case_id} 的实时调解方案查询")
                return self._process_active_mediation_plans(mediation_case, mediation_case_id)

        except serializers.ValidationError as e:
            # 处理序列化器验证错误
            error_messages = []
            if hasattr(e, "detail"):
                for _, messages in e.detail.items():
                    if isinstance(messages, list):
                        error_messages.extend([str(msg) for msg in messages])
                    else:
                        error_messages.append(str(messages))
            error_msg = "; ".join(error_messages) if error_messages else str(e)
            logger.error(f"参数验证失败: {error_msg}")
            return AjaxResult.fail(msg=f"参数验证失败: {error_msg}")

        except MediationCase.DoesNotExist:
            logger.error(f"调解案件 {mediation_case_id} 不存在")
            return AjaxResult(code=404, msg="未找到对应的调解案件", state="fail").to_json_response()

        except Exception as e:
            logger.error(f"处理调解方案配置获取请求时发生异常: {str(e)}")
            return AjaxResult.fail(msg="服务器内部错误")

    def _process_confirmed_plan_config(self, mediation_case, mediation_case_id):
        """
        处理确认的调解方案配置快照

        当调解案件存在confirmed_plan_config字段时，直接使用该配置进行表达式计算，
        无需查询数据库中的调解方案记录。返回格式与原有逻辑保持一致。

        Args:
            mediation_case: 调解案件对象
            mediation_case_id: 调解案件ID

        Returns:
            AjaxResult: 包含处理后的配置数据的响应对象
        """
        try:
            confirmed_config = mediation_case.confirmed_plan_config

            # 检查确认配置是否为空
            if not confirmed_config:
                logger.warning(f"调解案件 {mediation_case_id} 的confirmed_plan_config为空")
                return AjaxResult.success(msg="获取成功", data=[])

            # 创建方案分组结构（模拟原有格式，使用确认的配置）
            plan_group = {
                "plan_id": "confirmed",  # 使用特殊标识表示这是确认的配置
                "plan_name": "已确认的调解方案",
                "plan_config": [],
            }

            # 获取资产包和行号信息用于表达式计算
            asset_package = mediation_case.asset_package
            row_number = mediation_case.asset_package_row_number or 1

            # 处理确认的配置数据
            config_items = []
            if isinstance(confirmed_config, list):
                config_items = confirmed_config
            elif isinstance(confirmed_config, dict):
                config_items = [confirmed_config]
            else:
                logger.warning(
                    f"调解案件 {mediation_case_id} 的confirmed_plan_config格式不正确: {type(confirmed_config)}"
                )
                return AjaxResult.success(msg="获取成功", data=[])

            # 处理每个配置项
            for config_item in config_items:
                # 复制原始配置对象
                processed_item = config_item.copy() if isinstance(config_item, dict) else config_item

                # 检查配置对象是否包含必要字段
                if not isinstance(config_item, dict):
                    logger.warning(f"跳过非字典格式的配置项: {config_item}")
                    plan_group["plan_config"].append(processed_item)
                    continue

                logic_type = config_item.get("logic_type")
                expression = config_item.get("expression")

                # 如果没有logic_type或expression，直接添加到结果中
                if not logic_type or not expression:
                    logger.warning(f"配置项缺少logic_type或expression字段: {config_item}")
                    processed_item["value"] = None
                    plan_group["plan_config"].append(processed_item)
                    continue

                # 检查是否有关联的资产包用于表达式计算
                if not asset_package:
                    logger.warning(f"调解案件 {mediation_case_id} 没有关联资产包，无法进行表达式计算")
                    processed_item["value"] = None
                    plan_group["plan_config"].append(processed_item)
                    continue

                # 使用工具函数计算表达式
                calculation_result = calculate_expression_with_asset_data(
                    asset_package=asset_package,
                    row_number=row_number,
                    expression=expression,
                    logic_type=logic_type,
                )

                # 将计算结果添加到配置对象中
                if calculation_result["success"]:
                    processed_item["value"] = calculation_result["result"]
                    logger.info(
                        f"确认配置表达式计算成功，案件ID: {mediation_case_id}, 配置ID: {config_item.get('id', 'unknown')}, 结果: {calculation_result['result']}"
                    )
                else:
                    processed_item["value"] = None
                    logger.error(
                        f"确认配置表达式计算失败，案件ID: {mediation_case_id}, 配置ID: {config_item.get('id', 'unknown')}, 错误: {calculation_result['error']}"
                    )

                plan_group["plan_config"].append(processed_item)

            # 返回结果（保持数组格式以确保一致性）
            grouped_plan_configs = [plan_group]
            logger.info(
                f"确认方案配置处理完成，调解案件ID: {mediation_case_id}, 处理了 {len(plan_group['plan_config'])} 个配置项"
            )
            return AjaxResult.success(msg="获取成功", data=grouped_plan_configs)

        except Exception as e:
            logger.error(f"处理确认方案配置时发生异常: {str(e)}")
            return AjaxResult.fail(msg="处理确认方案配置失败")

    def _process_active_mediation_plans(self, mediation_case, mediation_case_id):
        """
        处理已生效的调解方案配置（原有逻辑）

        当调解案件的confirmed_plan_config字段为空时，查询相关的已生效调解方案，
        对每个方案的plan_config进行表达式计算处理。

        Args:
            mediation_case: 调解案件对象
            mediation_case_id: 调解案件ID

        Returns:
            AjaxResult: 包含处理后的配置数据的响应对象
        """
        try:
            # 查询相关的已生效调解方案
            # 查询条件：(asset_package=调解案件.asset_package 或 mediation_case=调解案件对象) 且 plan_status="active"
            query_conditions = Q(plan_status="active")

            if mediation_case.asset_package:
                query_conditions &= Q(asset_package=mediation_case.asset_package) | Q(mediation_case=mediation_case)
            else:
                query_conditions &= Q(mediation_case=mediation_case)

            # 按优先级排序：资产包关联的方案优先，然后是案件关联的方案
            mediation_plans = MediationPlan.objects.filter(query_conditions).order_by(
                Case(
                    When(asset_package__isnull=False, then=1),  # 资产包关联的方案优先
                    When(mediation_case__isnull=False, then=2),  # 案件关联的方案其次
                    default=3,
                    output_field=IntegerField(),
                ),
                "id",  # 相同优先级内按ID排序
            )

            # 检查是否找到相关的调解方案
            if not mediation_plans.exists():
                logger.warning(f"调解案件 {mediation_case_id} 没有找到相关的已生效调解方案")
                return AjaxResult.success(msg="获取成功", data=[])

            # 按调解方案分组处理配置信息
            grouped_plan_configs = []
            asset_package = mediation_case.asset_package

            # 获取调解案件在资产包中的行号，默认为1
            row_number = mediation_case.asset_package_row_number or 1

            # 遍历每个调解方案
            for plan in mediation_plans:
                if plan.asset_package is not None:
                    plan_name_mark = " [资产包]"
                else:
                    plan_name_mark = " [调解案件]"
                # 创建方案分组结构
                plan_group = {"plan_id": plan.id, "plan_name": plan.plan_name + plan_name_mark, "plan_config": []}

                # 处理当前方案的plan_config配置
                if plan.plan_config:
                    # 获取配置项列表
                    config_items = []
                    if isinstance(plan.plan_config, list):
                        config_items = plan.plan_config
                    elif isinstance(plan.plan_config, dict):
                        config_items = [plan.plan_config]
                    else:
                        logger.warning(f"调解方案 {plan.id} 的plan_config格式不正确: {type(plan.plan_config)}")
                        continue

                    # 处理当前方案的每个配置项
                    for config_item in config_items:
                        # 复制原始配置对象
                        processed_item = config_item.copy() if isinstance(config_item, dict) else config_item

                        # 检查配置对象是否包含必要字段
                        if not isinstance(config_item, dict):
                            logger.warning(f"跳过非字典格式的配置项: {config_item}")
                            plan_group["plan_config"].append(processed_item)
                            continue

                        logic_type = config_item.get("logic_type")
                        expression = config_item.get("expression")

                        # 如果没有logic_type或expression，直接添加到结果中
                        if not logic_type or not expression:
                            logger.warning(f"配置项缺少logic_type或expression字段: {config_item}")
                            processed_item["value"] = None
                            plan_group["plan_config"].append(processed_item)
                            continue

                        # 检查是否有关联的资产包用于表达式计算
                        if not asset_package:
                            logger.warning(f"调解案件 {mediation_case_id} 没有关联资产包，无法进行表达式计算")
                            processed_item["value"] = None
                            plan_group["plan_config"].append(processed_item)
                            continue

                        # 使用工具函数计算表达式
                        calculation_result = calculate_expression_with_asset_data(
                            asset_package=asset_package,
                            row_number=row_number,
                            expression=expression,
                            logic_type=logic_type,
                        )

                        # 将计算结果添加到配置对象中
                        if calculation_result["success"]:
                            processed_item["value"] = calculation_result["result"]
                            logger.info(
                                f"表达式计算成功，方案ID: {plan.id}, 配置ID: {config_item.get('id', 'unknown')}, 结果: {calculation_result['result']}"
                            )
                        else:
                            processed_item["value"] = None
                            logger.error(
                                f"表达式计算失败，方案ID: {plan.id}, 配置ID: {config_item.get('id', 'unknown')}, 错误: {calculation_result['error']}"
                            )

                        plan_group["plan_config"].append(processed_item)

                # 将方案分组添加到结果中（即使配置为空也添加，保持数据完整性）
                grouped_plan_configs.append(plan_group)

            # 统计处理的配置项总数
            total_config_count = sum(len(group["plan_config"]) for group in grouped_plan_configs)
            logger.info(
                f"调解方案配置处理完成，调解案件ID: {mediation_case_id}, 处理了 {len(grouped_plan_configs)} 个方案，共 {total_config_count} 个配置项"
            )
            return AjaxResult.success(msg="获取成功", data=grouped_plan_configs)

        except Exception as e:
            logger.error(f"处理已生效调解方案配置时发生异常: {str(e)}")
            return AjaxResult.fail(msg="处理调解方案配置失败")
