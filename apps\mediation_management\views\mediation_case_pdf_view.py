#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : mediation_case_pdf_view.py
<AUTHOR> JT_DA
@Date     : 2025/07/22
@File_Desc: 调解协议PDF生成视图
"""

import logging
import os
from django.core.files.base import ContentFile
from rest_framework.generics import GenericAPIView
from rest_framework import serializers

from reportlab.lib.pagesizes import A4
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib.enums import TA_CENTER
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Image, Table, TableStyle
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont

from apps.mediation_management.models import MediationCase, MediationCaseFile
from utils.ajax_result import AjaxResult
from utils.expression_calculator import calculate_expression_with_asset_data
from utils.file_security_helper import FileSecurityHelper

# 获取日志记录器
logger = logging.getLogger(__name__)


class MediationAgreementPDFSerializer(serializers.Serializer):
    """
    调解协议PDF生成接口的参数验证序列化器，用于规范化请求参数的格式和约束。
    该序列化器主要用于API文档自动生成和开发调试时的参数验证。
    """

    mediation_case_number = serializers.CharField(
        max_length=50, help_text="调解案件号，格式：GZTJ+YYYYMMDD+6位安全哈希，用于双重校验"
    )


class MediationAgreementPDFView(GenericAPIView):
    """
    调解协议PDF生成和下载视图

    提供调解协议PDF文件的生成和安全下载功能。该视图通过调解案件ID和调解案件号进行双重校验，
    确保数据安全性，生成包含调解信息、调解方案和电子签名的标准化PDF协议文件。
    生成的PDF文件将存储到调解案件的mediation_agreement字段中，并提供安全的下载链接。
    """

    # 配置序列化器类用于API文档生成
    serializer_class = MediationAgreementPDFSerializer

    # 配置权限类：需要用户认证
    # permission_classes = [MyPermission]  # 可根据需要调整权限

    def get(self, request, id, *args, **kwargs):
        """
        生成调解协议PDF文件并提供下载链接

        根据调解案件ID和调解案件号进行双重校验，生成包含调解信息、调解方案和电子签名的
        标准化PDF协议文件。生成的PDF文件将存储到调解案件对象中，并返回安全的下载链接。

        **请求参数：**

        **路径参数：**
        - id (整数, 必需): 调解案件ID，通过URL路径参数传递

        **查询参数：**
        - mediation_case_number (字符串, 必需): 调解案件号，用于双重校验，格式：GZTJ+YYYYMMDD+6位安全哈希

        **请求数据示例：**
        ```
        GET /mediation_management/mediation_case/123/agreement_pdf/?mediation_case_number=GZTJ20250801ABC123
        ```

        **响应数据结构：**

        成功响应：
        ```json
        {
            "code": 200,
            "msg": "PDF生成成功",
            "state": "success",
            "data": {
                "download_url": "/user/files/download/550e8400-e29b-41d4-a716-446655440000/",
                "file_name": "调解协议_GZTJ20250801ABC123.pdf",
                "file_size": 1024000
            }
        }
        ```

        错误响应：
        ```json
        {
            "code": 404,
            "msg": "调解案件不存在或案件号不匹配",
            "state": "fail"
        }
        ```
        ```json
        {
            "code": 400,
            "msg": "调解案件缺少必要的配置信息",
            "state": "fail"
        }
        ```
        """
        try:
            # 获取查询参数
            mediation_case_number = request.GET.get("mediation_case_number")
            if not mediation_case_number:
                logger.warning(f"缺少调解案件号参数，案件ID: {id}")
                return AjaxResult.fail(msg="缺少调解案件号参数")

            # 验证参数格式
            serializer_data = {"mediation_case_number": mediation_case_number}
            serializer = self.get_serializer(data=serializer_data)
            serializer.is_valid(raise_exception=True)

            # 记录请求日志
            logger.info(f"开始生成调解协议PDF，案件ID: {id}, 案件号: {mediation_case_number}")

            # 双重校验：通过案件ID和案件号查询调解案件
            try:
                mediation_case = MediationCase.objects.get(id=id, case_number=mediation_case_number)
            except MediationCase.DoesNotExist:
                logger.warning(f"调解案件不存在或案件号不匹配，案件ID: {id}, 案件号: {mediation_case_number}")
                return AjaxResult.not_found(msg="调解案件不存在或案件号不匹配")

            # 验证必要的数据是否存在
            if not mediation_case.confirmed_mediation_config and not mediation_case.confirmed_plan_config:
                logger.warning(f"调解案件 {id} 缺少确认的配置信息")
                return AjaxResult.fail(msg="调解案件缺少必要的配置信息")

            # 生成PDF文件
            pdf_content = self._generate_pdf_content(mediation_case)
            if not pdf_content:
                logger.error(f"PDF内容生成失败，案件ID: {id}")
                return AjaxResult.fail(msg="PDF生成失败")

            # 创建MediationCaseFile实例来存储PDF文件
            file_name = f"调解协议_{mediation_case_number}.pdf"

            # 创建MediationCaseFile对象
            case_file = MediationCaseFile(file_name=file_name)
            case_file.file.save(file_name, ContentFile(pdf_content), save=False)
            case_file.save()

            # 清理该调解案件已有的同名PDF文件记录
            self._cleanup_existing_pdf_files(mediation_case, file_name)

            # 将文件关联到调解案件的附件中
            mediation_case.attachments.add(case_file)

            # 使用FileSecurityHelper生成安全下载链接
            download_url = FileSecurityHelper.generate_secure_download_url(case_file)
            if not download_url:
                logger.warning(f"无法生成安全下载链接，案件ID: {id}")
                # 如果无法生成安全链接，返回错误
                return AjaxResult.fail(msg="无法生成安全下载链接")

            # 构建响应数据
            response_data = {
                "download_url": download_url,
                "file_name": file_name,
                "file_size": len(pdf_content) if pdf_content else 0,
                "secure_token": str(case_file.secure_token) if case_file.secure_token else None,
            }

            logger.info(f"调解协议PDF生成成功，案件ID: {id}, 文件大小: {len(pdf_content)} bytes")
            return AjaxResult.success(msg="PDF生成成功", data=response_data)

        except serializers.ValidationError as e:
            # 参数验证失败
            error_messages = []
            if hasattr(e, "detail"):
                for _, messages in e.detail.items():
                    if isinstance(messages, list):
                        error_messages.extend([str(msg) for msg in messages])
                    else:
                        error_messages.append(str(messages))
            error_msg = "; ".join(error_messages) if error_messages else str(e)
            logger.error(f"参数验证失败: {error_msg}")
            return AjaxResult.fail(msg=f"参数验证失败: {error_msg}")

        except Exception as e:
            logger.error(f"生成调解协议PDF时发生异常: {str(e)}")
            return AjaxResult.fail(msg="PDF生成失败")

    def _generate_pdf_content(self, mediation_case):
        """
        生成调解协议PDF内容

        根据调解案件对象生成标准化的调解协议PDF文件内容，包含标题、案件信息、
        调解信息、调解方案和电子签名等完整内容。支持中文字体显示。

        Args:
            mediation_case: 调解案件对象

        Returns:
            bytes: PDF文件的二进制内容，如果生成失败则返回None
        """
        try:
            # 注册中文字体
            chinese_font_name = self._register_chinese_font()

            # 创建内存中的PDF文档
            from io import BytesIO

            buffer = BytesIO()

            # 创建PDF文档对象
            doc = SimpleDocTemplate(buffer, pagesize=A4, rightMargin=72, leftMargin=72, topMargin=72, bottomMargin=72)

            # 获取样式表
            styles = getSampleStyleSheet()

            # 创建支持中文的自定义样式
            title_style = ParagraphStyle(
                "CustomTitle",
                parent=styles["Heading1"],
                fontSize=18,
                spaceAfter=30,
                alignment=TA_CENTER,
                fontName=chinese_font_name,
            )

            heading_style = ParagraphStyle(
                "CustomHeading",
                parent=styles["Heading2"],
                fontSize=14,
                spaceAfter=12,
                spaceBefore=20,
                fontName=chinese_font_name,
            )

            normal_style = ParagraphStyle(
                "CustomNormal", parent=styles["Normal"], fontSize=12, spaceAfter=6, fontName=chinese_font_name
            )

            # 构建PDF内容
            story = []

            # 1. 标题
            title = Paragraph("调解协议", title_style)
            story.append(title)
            story.append(Spacer(1, 20))

            # 2. 左上角信息
            case_info = [
                f"调解案件号：{mediation_case.case_number}",
                f"发起日期：{mediation_case.initiate_date.strftime('%Y-%m-%d') if mediation_case.initiate_date else '未设置'}",
                f"债权人：{mediation_case.creditor.creditor_name if mediation_case.creditor else '未设置'}",
                f"债务人：{mediation_case.debtor.debtor_name if mediation_case.debtor else '未设置'}",
            ]

            for info in case_info:
                story.append(Paragraph(info, normal_style))
            story.append(Spacer(1, 20))

            # 3. 调解信息部分
            if mediation_case.confirmed_mediation_config:
                story.append(Paragraph("调解信息", heading_style))
                mediation_items = self._process_config_data(mediation_case, mediation_case.confirmed_mediation_config)
                for item in mediation_items:
                    content = f"{item.get('title', '')}：{item.get('value', '')}"
                    story.append(Paragraph(content, normal_style))
                story.append(Spacer(1, 15))

            # 4. 调解方案部分
            if mediation_case.confirmed_plan_config:
                story.append(Paragraph("调解方案", heading_style))
                plan_items = self._process_config_data(mediation_case, mediation_case.confirmed_plan_config)
                for item in plan_items:
                    content = f"{item.get('title', '')}：{item.get('value', '')}"
                    story.append(Paragraph(content, normal_style))
                story.append(Spacer(1, 30))

            # 5. 右下角签名信息
            # 构建签署人信息（第一行）
            signer_info = []

            # 添加电子签名图片（如果存在）
            if mediation_case.electronic_signature:
                try:
                    # 检查电子签名文件是否存在
                    if os.path.exists(mediation_case.electronic_signature.path):
                        signature_img = Image(mediation_case.electronic_signature.path, width=2 * inch, height=1 * inch)
                        signer_info.append(signature_img)
                except Exception as e:
                    logger.warning(f"无法加载电子签名图片: {str(e)}")
                    signer_info.append(Paragraph("签署人：[电子签名]", normal_style))
            else:
                signer_info.append(Paragraph("签署人：[未签署]", normal_style))

            # 构建签署日期信息（第二行）
            date_info = []
            if mediation_case.signature_date:
                # 如果签署日期不为NULL，显示格式化日期
                sign_date = mediation_case.signature_date.strftime("%Y年%m月%d日")
                date_info.append(Paragraph(f"签署日期：{sign_date}", normal_style))
            else:
                # 如果签署日期为NULL，显示未签署
                date_info.append(Paragraph("签署日期：[未签署]", normal_style))

            # 添加空白间距，将签名信息推到页面底部
            story.append(Spacer(1, 50))

            # 创建两行签名表格（右对齐，紧贴右下角）
            signature_data = [signer_info, date_info]  # 第一行：签署人信息  # 第二行：签署日期信息

            # 计算表格宽度，确保右对齐效果
            page_width = A4[0] - 144  # 页面宽度减去左右边距（72*2）
            table_width = 3 * inch  # 签名表格宽度

            signature_table = Table(signature_data, colWidths=[table_width])
            signature_table.setStyle(
                TableStyle(
                    [
                        ("ALIGN", (0, 0), (-1, -1), "RIGHT"),  # 内容右对齐
                        ("VALIGN", (0, 0), (-1, -1), "MIDDLE"),  # 垂直居中对齐
                        ("RIGHTPADDING", (0, 0), (-1, -1), 0),  # 右边距为0
                        ("LEFTPADDING", (0, 0), (-1, -1), 0),  # 左边距为0
                        ("BOTTOMPADDING", (0, 0), (-1, -1), 4),  # 行间距
                        ("TOPPADDING", (0, 0), (-1, -1), 4),  # 行间距
                        ("LINEBELOW", (0, 0), (-1, 0), 0, "white"),  # 第一行下方无边框
                    ]
                )
            )

            # 创建一个包装表格来实现右对齐定位
            wrapper_data = [["", signature_table]]
            wrapper_table = Table(wrapper_data, colWidths=[page_width - table_width, table_width])
            wrapper_table.setStyle(
                TableStyle(
                    [
                        ("ALIGN", (1, 0), (1, 0), "RIGHT"),  # 签名表格右对齐
                        ("VALIGN", (0, 0), (-1, -1), "BOTTOM"),  # 底部对齐
                        ("LEFTPADDING", (0, 0), (-1, -1), 0),  # 无边距
                        ("RIGHTPADDING", (0, 0), (-1, -1), 0),  # 无边距
                        ("TOPPADDING", (0, 0), (-1, -1), 0),  # 无边距
                        ("BOTTOMPADDING", (0, 0), (-1, -1), 0),  # 无边距
                    ]
                )
            )

            story.append(wrapper_table)

            # 生成PDF
            doc.build(story)

            # 获取PDF内容
            pdf_content = buffer.getvalue()
            buffer.close()

            logger.info(f"PDF内容生成成功，大小: {len(pdf_content)} bytes")
            return pdf_content

        except Exception as e:
            logger.error(f"生成PDF内容时发生异常: {str(e)}")
            return None

    def _process_config_data(self, mediation_case, config_data):
        """
        处理配置数据，计算表达式并返回格式化的结果

        对调解配置或方案配置中的表达式进行计算处理，返回包含标题和计算结果的列表。

        Args:
            mediation_case: 调解案件对象
            config_data: 配置数据（JSON格式的列表）

        Returns:
            list: 包含处理后数据的列表，每个元素包含title和value字段
        """
        try:
            processed_items = []

            if not config_data or not isinstance(config_data, list):
                logger.warning("配置数据为空或格式不正确")
                return processed_items

            for item in config_data:
                if not isinstance(item, dict):
                    continue

                title = item.get("title", "")
                expression = item.get("expression", "")
                logic_type = item.get("logic_type", "text_formatting")

                # 如果没有表达式，直接使用原始值
                if not expression:
                    value = item.get("value", "")
                else:
                    # 使用表达式计算器处理表达式
                    if mediation_case.asset_package and mediation_case.asset_package_row_number:
                        calc_result = calculate_expression_with_asset_data(
                            mediation_case.asset_package,
                            mediation_case.asset_package_row_number,
                            expression,
                            logic_type,
                        )
                        if calc_result.get("success"):
                            value = calc_result.get("result", "")
                        else:
                            logger.warning(f"表达式计算失败: {calc_result.get('error', '')}")
                            value = item.get("value", expression)  # 使用原始值作为备用
                    else:
                        logger.warning("缺少资产包或行号信息，无法计算表达式")
                        value = item.get("value", expression)  # 使用原始值作为备用

                processed_items.append({"title": title, "value": str(value) if value is not None else ""})

            return processed_items

        except Exception as e:
            logger.error(f"处理配置数据时发生异常: {str(e)}")
            return []

    def _register_chinese_font(self):
        """
        注册中文字体以支持PDF中的中文显示

        尝试注册系统中可用的中文字体，提供跨平台兼容性。
        优先级：微软雅黑 > 黑体 > 宋体 > 默认字体

        Returns:
            str: 注册成功的字体名称
        """
        try:
            # 定义中文字体候选列表（按优先级排序）
            font_candidates = [
                # Windows 系统字体
                {
                    "name": "Microsoft-YaHei",
                    "paths": [
                        "C:/Windows/Fonts/msyh.ttc",  # 微软雅黑
                        "C:/Windows/Fonts/msyh.ttf",
                        "/System/Library/Fonts/PingFang.ttc",  # macOS 苹方字体
                        "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",  # Linux 备选
                    ],
                },
                {
                    "name": "SimHei",
                    "paths": [
                        "C:/Windows/Fonts/simhei.ttf",  # 黑体
                        "/System/Library/Fonts/STHeiti Light.ttc",  # macOS 黑体
                        "/usr/share/fonts/truetype/wqy/wqy-zenhei.ttc",  # Linux 文泉驿正黑
                    ],
                },
                {
                    "name": "SimSun",
                    "paths": [
                        "C:/Windows/Fonts/simsun.ttc",  # 宋体
                        "C:/Windows/Fonts/simsun.ttf",
                        "/System/Library/Fonts/Songti.ttc",  # macOS 宋体
                        "/usr/share/fonts/truetype/arphic/uming.ttc",  # Linux AR PL UMing
                    ],
                },
            ]

            # 尝试注册字体
            for font_info in font_candidates:
                font_name = font_info["name"]

                # 检查字体是否已经注册
                try:
                    # 尝试使用字体，如果已注册则直接返回
                    pdfmetrics.getFont(font_name)
                    logger.info(f"字体 {font_name} 已注册，直接使用")
                    return font_name
                except:
                    # 字体未注册，继续尝试注册
                    pass

                # 尝试从候选路径中找到字体文件并注册
                for font_path in font_info["paths"]:
                    if os.path.exists(font_path):
                        try:
                            # 注册字体
                            pdfmetrics.registerFont(TTFont(font_name, font_path))
                            logger.info(f"成功注册中文字体: {font_name} from {font_path}")
                            return font_name
                        except Exception as e:
                            logger.warning(f"注册字体 {font_name} 失败: {str(e)}")
                            continue

            # 如果所有中文字体都注册失败，尝试注册 ReportLab 内置的中文字体
            try:
                from reportlab.pdfbase.cidfonts import UnicodeCIDFont

                pdfmetrics.registerFont(UnicodeCIDFont("STSong-Light"))
                logger.info("使用 ReportLab 内置中文字体: STSong-Light")
                return "STSong-Light"
            except Exception as e:
                logger.warning(f"注册内置中文字体失败: {str(e)}")

            # 最后的备选方案：使用 Helvetica 字体（不支持中文，但不会报错）
            logger.warning("所有中文字体注册失败，使用 Helvetica 字体（可能无法正确显示中文）")
            return "Helvetica"

        except Exception as e:
            logger.error(f"注册中文字体时发生异常: {str(e)}")
            return "Helvetica"

    def _cleanup_existing_pdf_files(self, mediation_case, target_file_name):
        """
        清理调解案件已有的同名PDF文件记录

        在保存新的PDF文件之前，删除该调解案件中已存在的同名PDF文件，
        包括从attachments关系中移除、删除数据库记录和物理文件。

        Args:
            mediation_case: 调解案件对象
            target_file_name: 目标文件名，格式为 "调解协议_{mediation_case_number}.pdf"
        """
        try:
            # 查找该调解案件附件中的同名PDF文件
            existing_files = mediation_case.attachments.filter(file_name=target_file_name)

            if existing_files.exists():
                logger.info(f"发现 {existing_files.count()} 个同名PDF文件，开始清理: {target_file_name}")

                for existing_file in existing_files:
                    try:
                        # 1. 从调解案件的attachments关系中移除
                        mediation_case.attachments.remove(existing_file)
                        logger.info(f"已从attachments中移除文件: {existing_file.file_name}")

                        # 2. 删除物理文件（如果存在）
                        if existing_file.file and os.path.exists(existing_file.file.path):
                            try:
                                os.remove(existing_file.file.path)
                                logger.info(f"已删除物理文件: {existing_file.file.path}")
                            except OSError as e:
                                logger.warning(f"删除物理文件失败: {existing_file.file.path}, 错误: {str(e)}")

                        # 3. 删除MediationCaseFile数据库记录
                        existing_file.delete()
                        logger.info(f"已删除数据库记录: {existing_file.file_name}")

                    except Exception as e:
                        logger.error(f"删除单个文件记录时发生异常: {existing_file.file_name}, 错误: {str(e)}")
                        # 继续处理其他文件，不中断整个清理过程
                        continue

                logger.info(f"同名PDF文件清理完成: {target_file_name}")
            else:
                logger.info(f"未发现同名PDF文件，无需清理: {target_file_name}")

        except Exception as e:
            logger.error(f"清理旧PDF文件时发生异常: {str(e)}")
            # 即使清理失败，也不影响新文件的生成和保存
            logger.warning("清理旧文件失败，但将继续保存新文件")
